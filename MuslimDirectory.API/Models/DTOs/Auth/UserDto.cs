namespace MuslimDirectory.API.Models.DTOs.Auth;

public class UserDto
{
    public Guid Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string UserType { get; set; } = string.Empty;
    public string PreferredLanguage { get; set; } = string.Empty;
    public string? ProfilePicture { get; set; }
    public bool IsEmailVerified { get; set; }
}
