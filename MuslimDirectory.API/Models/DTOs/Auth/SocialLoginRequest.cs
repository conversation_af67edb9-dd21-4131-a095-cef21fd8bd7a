using System.ComponentModel.DataAnnotations;

namespace MuslimDirectory.API.Models.DTOs.Auth;

public class SocialLoginRequest
{
    [Required(ErrorMessage = "Provider is required")]
    [RegularExpression(@"^(Google|Apple)$", ErrorMessage = "Provider must be 'Google' or 'Apple'")]
    public string Provider { get; set; } = string.Empty;

    [Required(ErrorMessage = "Provider token is required")]
    public string ProviderToken { get; set; } = string.Empty;

    public string? FirstName { get; set; }
    
    public string? LastName { get; set; }
    
    public string? Email { get; set; }
    
    public string? ProfilePicture { get; set; }
}
