using XGENO.DBHelpers.Core.Attributes;

namespace MuslimDirectory.API.Models.Entities;

public class UserAuthProvider
{
    [Column("ID")]
    public Guid Id { get; set; }
    
    [Column("UserID")]
    public Guid UserId { get; set; }
    
    public string Provider { get; set; } = string.Empty;
    
    [Column("ProviderID")]
    public string ProviderId { get; set; } = string.Empty;
    
    public DateTime CreatedAt { get; set; }
}
