using XGENO.DBHelpers.Core.Attributes;

namespace MuslimDirectory.API.Models.Entities;

public class PasswordResetToken
{
    [Column("ID")]
    public Guid Id { get; set; }
    
    [Column("UserID")]
    public Guid UserId { get; set; }
    
    public string Token { get; set; } = string.Empty;
    
    public DateTime ExpiresAt { get; set; }
    
    public bool IsUsed { get; set; }
    
    public DateTime CreatedAt { get; set; }
}
