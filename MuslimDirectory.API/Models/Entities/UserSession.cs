using XGENO.DBHelpers.Core.Attributes;

namespace MuslimDirectory.API.Models.Entities;

public class UserSession
{
    [Column("ID")]
    public Guid Id { get; set; }
    
    [Column("UserID")]
    public Guid UserId { get; set; }
    
    public string Token { get; set; } = string.Empty;
    
    public DateTime ExpiresAt { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; }
}
