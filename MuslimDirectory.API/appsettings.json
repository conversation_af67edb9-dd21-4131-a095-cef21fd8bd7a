{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=salaamprojects_muslim_directory_dev_db;Trusted_Connection=true;TrustServerCertificate=true;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "MuslimDirectory.API", "Audience": "MuslimDirectory.Client", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderName": "Muslim Directory", "Username": "", "Password": ""}}