using Microsoft.Data.SqlClient;
using MuslimDirectory.API.Models.DTOs.Auth;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Models.Entities;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;
using XGENO.DBHelpers.Extensions;

namespace MuslimDirectory.API.Services.Implementations;

public class AuthService : IAuthService
{
    private readonly string _connectionString;
    private readonly IPasswordService _passwordService;
    private readonly IJwtService _jwtService;

    public AuthService(IConfiguration configuration, IPasswordService passwordService, IJwtService jwtService)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new ArgumentNullException("Connection string not found");
        _passwordService = passwordService;
        _jwtService = jwtService;
    }

    public async Task<ApiResponse<AuthResponse>> SignupAsync(SignupRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user already exists
            var existingUsers = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE Email = @Email",
                request.Email.ToSqlParam("Email"));

            if (existingUsers.Any())
            {
                return ApiResponse<AuthResponse>.ErrorResponse("USER_EXISTS", "User with this email already exists", null, "email");
            }

            // Create new user
            var userId = Guid.NewGuid();
            var hashedPassword = _passwordService.HashPassword(request.Password);
            var verificationToken = Guid.NewGuid().ToString();

            await connection.ExecuteNonQueryText(@"
                INSERT INTO Users (ID, Email, PasswordHash, FirstName, LastName, PhoneNumber, Country, City, 
                                 PreferredLanguage, Gender, IsEmailVerified, IsPhoneVerified, IsActive, UserType, CreatedAt, UpdatedAt)
                VALUES (@ID, @Email, @PasswordHash, @FirstName, @LastName, @PhoneNumber, @Country, @City, 
                        @PreferredLanguage, @Gender, @IsEmailVerified, @IsPhoneVerified, @IsActive, @UserType, @CreatedAt, @UpdatedAt)",
                userId.ToSqlParam("ID"),
                request.Email.ToSqlParam("Email"),
                hashedPassword.ToSqlParam("PasswordHash"),
                request.FirstName.ToSqlParam("FirstName"),
                request.LastName.ToSqlParam("LastName"),
                request.PhoneNumber.ToSqlParam("PhoneNumber"),
                request.Country.ToSqlParam("Country"),
                request.City.ToSqlParam("City"),
                request.PreferredLanguage.ToSqlParam("PreferredLanguage"),
                request.Gender.ToSqlParam("Gender"),
                false.ToSqlParam("IsEmailVerified"),
                false.ToSqlParam("IsPhoneVerified"),
                true.ToSqlParam("IsActive"),
                "Regular".ToSqlParam("UserType"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"),
                DateTime.UtcNow.ToSqlParam("UpdatedAt"));

            // Create email verification token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO PasswordResetTokens (ID, UserID, Token, ExpiresAt, IsUsed, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsUsed, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                userId.ToSqlParam("UserID"),
                verificationToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddHours(24).ToSqlParam("ExpiresAt"),
                false.ToSqlParam("IsUsed"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            // Get created user
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE ID = @ID",
                userId.ToSqlParam("ID"));

            var user = users.First();

            // Generate JWT token
            var token = _jwtService.GenerateToken(user);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Store refresh token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO UserSessions (ID, UserID, Token, ExpiresAt, IsActive, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsActive, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                userId.ToSqlParam("UserID"),
                refreshToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddDays(7).ToSqlParam("ExpiresAt"),
                true.ToSqlParam("IsActive"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            var response = new AuthResponse
            {
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    UserType = user.UserType,
                    PreferredLanguage = user.PreferredLanguage,
                    ProfilePicture = user.ProfilePicture,
                    IsEmailVerified = user.IsEmailVerified
                },
                Token = token,
                RefreshToken = refreshToken
            };

            return ApiResponse<AuthResponse>.SuccessResponse(response, "User registered successfully. Please check your email for verification.");
        }
        catch (Exception ex)
        {
            return ApiResponse<AuthResponse>.ErrorResponse("SIGNUP_FAILED", "Failed to create user account", ex.Message);
        }
    }

    public async Task<ApiResponse<AuthResponse>> SigninAsync(SigninRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get user by email
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE Email = @Email AND IsActive = 1",
                request.Email.ToSqlParam("Email"));

            if (!users.Any())
            {
                return ApiResponse<AuthResponse>.ErrorResponse("INVALID_CREDENTIALS", "Invalid email or password");
            }

            var user = users.First();

            // Verify password
            if (string.IsNullOrEmpty(user.PasswordHash) || !_passwordService.VerifyPassword(request.Password, user.PasswordHash))
            {
                return ApiResponse<AuthResponse>.ErrorResponse("INVALID_CREDENTIALS", "Invalid email or password");
            }

            // Update last login
            await connection.ExecuteNonQueryText(
                "UPDATE Users SET LastLoginAt = @LastLoginAt WHERE ID = @ID",
                DateTime.UtcNow.ToSqlParam("LastLoginAt"),
                user.Id.ToSqlParam("ID"));

            // Generate tokens
            var token = _jwtService.GenerateToken(user);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Store refresh token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO UserSessions (ID, UserID, Token, ExpiresAt, IsActive, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsActive, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                user.Id.ToSqlParam("UserID"),
                refreshToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddDays(7).ToSqlParam("ExpiresAt"),
                true.ToSqlParam("IsActive"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            var response = new AuthResponse
            {
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    UserType = user.UserType,
                    PreferredLanguage = user.PreferredLanguage,
                    ProfilePicture = user.ProfilePicture,
                    IsEmailVerified = user.IsEmailVerified
                },
                Token = token,
                RefreshToken = refreshToken
            };

            return ApiResponse<AuthResponse>.SuccessResponse(response, "Login successful");
        }
        catch (Exception ex)
        {
            return ApiResponse<AuthResponse>.ErrorResponse("SIGNIN_FAILED", "Failed to sign in", ex.Message);
        }
    }

    public async Task<ApiResponse<AuthResponse>> SocialLoginAsync(SocialLoginRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // TODO: Validate provider token with the respective provider (Google/Apple)
            // For now, we'll assume the token is valid and proceed

            if (string.IsNullOrEmpty(request.Email))
            {
                return ApiResponse<AuthResponse>.ErrorResponse("EMAIL_REQUIRED", "Email is required for social login");
            }

            // Check if user exists
            var existingUsers = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE Email = @Email",
                request.Email.ToSqlParam("Email"));

            User user;
            bool isNewUser = false;

            if (existingUsers.Any())
            {
                user = existingUsers.First();

                // Check if this social provider is already linked
                var authProviders = await connection.ExecuteQuery<UserAuthProvider>(
                    "SELECT * FROM UserAuthProviders WHERE UserID = @UserID AND Provider = @Provider",
                    user.Id.ToSqlParam("UserID"),
                    request.Provider.ToSqlParam("Provider"));

                if (!authProviders.Any())
                {
                    // Link this social provider to existing user
                    await connection.ExecuteNonQueryText(@"
                        INSERT INTO UserAuthProviders (ID, UserID, Provider, ProviderID, CreatedAt)
                        VALUES (@ID, @UserID, @Provider, @ProviderID, @CreatedAt)",
                        Guid.NewGuid().ToSqlParam("ID"),
                        user.Id.ToSqlParam("UserID"),
                        request.Provider.ToSqlParam("Provider"),
                        request.ProviderToken.ToSqlParam("ProviderID"), // Using token as provider ID for now
                        DateTime.UtcNow.ToSqlParam("CreatedAt"));
                }
            }
            else
            {
                // Create new user
                var userId = Guid.NewGuid();
                isNewUser = true;

                await connection.ExecuteNonQueryText(@"
                    INSERT INTO Users (ID, Email, FirstName, LastName, ProfilePicture, IsEmailVerified,
                                     IsPhoneVerified, IsActive, UserType, CreatedAt, UpdatedAt)
                    VALUES (@ID, @Email, @FirstName, @LastName, @ProfilePicture, @IsEmailVerified,
                            @IsPhoneVerified, @IsActive, @UserType, @CreatedAt, @UpdatedAt)",
                    userId.ToSqlParam("ID"),
                    request.Email.ToSqlParam("Email"),
                    request.FirstName.ToSqlParam("FirstName"),
                    request.LastName.ToSqlParam("LastName"),
                    request.ProfilePicture.ToSqlParam("ProfilePicture"),
                    true.ToSqlParam("IsEmailVerified"), // Social login emails are considered verified
                    false.ToSqlParam("IsPhoneVerified"),
                    true.ToSqlParam("IsActive"),
                    "Regular".ToSqlParam("UserType"),
                    DateTime.UtcNow.ToSqlParam("CreatedAt"),
                    DateTime.UtcNow.ToSqlParam("UpdatedAt"));

                // Link social provider
                await connection.ExecuteNonQueryText(@"
                    INSERT INTO UserAuthProviders (ID, UserID, Provider, ProviderID, CreatedAt)
                    VALUES (@ID, @UserID, @Provider, @ProviderID, @CreatedAt)",
                    Guid.NewGuid().ToSqlParam("ID"),
                    userId.ToSqlParam("UserID"),
                    request.Provider.ToSqlParam("Provider"),
                    request.ProviderToken.ToSqlParam("ProviderID"), // Using token as provider ID for now
                    DateTime.UtcNow.ToSqlParam("CreatedAt"));

                // Get created user
                var users = await connection.ExecuteQuery<User>(
                    "SELECT * FROM Users WHERE ID = @ID",
                    userId.ToSqlParam("ID"));

                user = users.First();
            }

            // Update last login
            await connection.ExecuteNonQueryText(
                "UPDATE Users SET LastLoginAt = @LastLoginAt WHERE ID = @ID",
                DateTime.UtcNow.ToSqlParam("LastLoginAt"),
                user.Id.ToSqlParam("ID"));

            // Generate tokens
            var token = _jwtService.GenerateToken(user);
            var refreshToken = _jwtService.GenerateRefreshToken();

            // Store refresh token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO UserSessions (ID, UserID, Token, ExpiresAt, IsActive, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsActive, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                user.Id.ToSqlParam("UserID"),
                refreshToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddDays(7).ToSqlParam("ExpiresAt"),
                true.ToSqlParam("IsActive"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            var response = new AuthResponse
            {
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    UserType = user.UserType,
                    PreferredLanguage = user.PreferredLanguage,
                    ProfilePicture = user.ProfilePicture,
                    IsEmailVerified = user.IsEmailVerified
                },
                Token = token,
                RefreshToken = refreshToken
            };

            var message = isNewUser ? "Account created and logged in successfully" : "Logged in successfully";
            return ApiResponse<AuthResponse>.SuccessResponse(response, message, request.Provider);
        }
        catch (Exception ex)
        {
            return ApiResponse<AuthResponse>.ErrorResponse("SOCIAL_LOGIN_FAILED", "Failed to authenticate with social provider", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE Email = @Email AND IsActive = 1",
                request.Email.ToSqlParam("Email"));

            if (!users.Any())
            {
                // Don't reveal if email exists or not for security
                return ApiResponse<string>.SuccessResponse("If the email exists, a password reset link has been sent.");
            }

            var user = users.First();
            var resetToken = Guid.NewGuid().ToString();

            // Invalidate existing reset tokens
            await connection.ExecuteNonQueryText(
                "UPDATE PasswordResetTokens SET IsUsed = 1 WHERE UserID = @UserID AND IsUsed = 0",
                user.Id.ToSqlParam("UserID"));

            // Create new reset token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO PasswordResetTokens (ID, UserID, Token, ExpiresAt, IsUsed, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsUsed, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                user.Id.ToSqlParam("UserID"),
                resetToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddHours(1).ToSqlParam("ExpiresAt"),
                false.ToSqlParam("IsUsed"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            // TODO: Send email with reset token
            // For now, we'll just return success

            return ApiResponse<string>.SuccessResponse("If the email exists, a password reset link has been sent.");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("FORGOT_PASSWORD_FAILED", "Failed to process password reset request", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Validate reset token
            var tokens = await connection.ExecuteQuery<PasswordResetToken>(@"
                SELECT * FROM PasswordResetTokens
                WHERE Token = @Token AND IsUsed = 0 AND ExpiresAt > @Now",
                request.Token.ToSqlParam("Token"),
                DateTime.UtcNow.ToSqlParam("Now"));

            if (!tokens.Any())
            {
                return ApiResponse<string>.ErrorResponse("INVALID_TOKEN", "Invalid or expired reset token");
            }

            var token = tokens.First();
            var hashedPassword = _passwordService.HashPassword(request.NewPassword);

            // Update password
            await connection.ExecuteNonQueryText(
                "UPDATE Users SET PasswordHash = @PasswordHash, UpdatedAt = @UpdatedAt WHERE ID = @ID",
                hashedPassword.ToSqlParam("PasswordHash"),
                DateTime.UtcNow.ToSqlParam("UpdatedAt"),
                token.UserId.ToSqlParam("ID"));

            // Mark token as used
            await connection.ExecuteNonQueryText(
                "UPDATE PasswordResetTokens SET IsUsed = 1 WHERE ID = @ID",
                token.Id.ToSqlParam("ID"));

            // Invalidate all user sessions
            await connection.ExecuteNonQueryText(
                "UPDATE UserSessions SET IsActive = 0 WHERE UserID = @UserID",
                token.UserId.ToSqlParam("UserID"));

            return ApiResponse<string>.SuccessResponse("Password reset successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("RESET_PASSWORD_FAILED", "Failed to reset password", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> VerifyEmailAsync(VerifyEmailRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Find verification token
            var tokens = await connection.ExecuteQuery<PasswordResetToken>(@"
                SELECT * FROM PasswordResetTokens
                WHERE Token = @Token AND IsUsed = 0 AND ExpiresAt > @Now",
                request.Token.ToSqlParam("Token"),
                DateTime.UtcNow.ToSqlParam("Now"));

            if (!tokens.Any())
            {
                return ApiResponse<string>.ErrorResponse("INVALID_TOKEN", "Invalid or expired verification token");
            }

            var token = tokens.First();

            // Update user email verification status
            await connection.ExecuteNonQueryText(
                "UPDATE Users SET IsEmailVerified = 1, UpdatedAt = @UpdatedAt WHERE ID = @ID",
                DateTime.UtcNow.ToSqlParam("UpdatedAt"),
                token.UserId.ToSqlParam("ID"));

            // Mark token as used
            await connection.ExecuteNonQueryText(
                "UPDATE PasswordResetTokens SET IsUsed = 1 WHERE ID = @ID",
                token.Id.ToSqlParam("ID"));

            return ApiResponse<string>.SuccessResponse("Email verified successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("EMAIL_VERIFICATION_FAILED", "Failed to verify email", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> ResendVerificationAsync(ResendVerificationRequest request)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Check if user exists
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE Email = @Email AND IsActive = 1",
                request.Email.ToSqlParam("Email"));

            if (!users.Any())
            {
                // Don't reveal if email exists or not for security
                return ApiResponse<string>.SuccessResponse("If the email exists and is not verified, a verification link has been sent.");
            }

            var user = users.First();

            if (user.IsEmailVerified)
            {
                return ApiResponse<string>.ErrorResponse("EMAIL_ALREADY_VERIFIED", "Email is already verified");
            }

            var verificationToken = Guid.NewGuid().ToString();

            // Invalidate existing verification tokens
            await connection.ExecuteNonQueryText(
                "UPDATE PasswordResetTokens SET IsUsed = 1 WHERE UserID = @UserID AND IsUsed = 0",
                user.Id.ToSqlParam("UserID"));

            // Create new verification token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO PasswordResetTokens (ID, UserID, Token, ExpiresAt, IsUsed, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsUsed, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                user.Id.ToSqlParam("UserID"),
                verificationToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddHours(24).ToSqlParam("ExpiresAt"),
                false.ToSqlParam("IsUsed"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            // TODO: Send email with verification token
            // For now, we'll just return success

            return ApiResponse<string>.SuccessResponse("Verification email sent successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("RESEND_VERIFICATION_FAILED", "Failed to resend verification email", ex.Message);
        }
    }

    public async Task<ApiResponse<AuthResponse>> RefreshTokenAsync(RefreshTokenRequest request)
    {
        try
        {
            // Validate the access token (even if expired)
            var principal = _jwtService.GetPrincipalFromExpiredToken(request.AccessToken);
            if (principal == null)
            {
                return ApiResponse<AuthResponse>.ErrorResponse("INVALID_TOKEN", "Invalid access token");
            }

            var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return ApiResponse<AuthResponse>.ErrorResponse("INVALID_TOKEN", "Invalid user ID in token");
            }

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Get user
            var users = await connection.ExecuteQuery<User>(
                "SELECT * FROM Users WHERE ID = @ID AND IsActive = 1",
                userId.ToSqlParam("ID"));

            if (!users.Any())
            {
                return ApiResponse<AuthResponse>.ErrorResponse("USER_NOT_FOUND", "User not found");
            }

            var user = users.First();

            // Generate new tokens
            var newToken = _jwtService.GenerateToken(user);
            var newRefreshToken = _jwtService.GenerateRefreshToken();

            // Store new refresh token
            await connection.ExecuteNonQueryText(@"
                INSERT INTO UserSessions (ID, UserID, Token, ExpiresAt, IsActive, CreatedAt)
                VALUES (@ID, @UserID, @Token, @ExpiresAt, @IsActive, @CreatedAt)",
                Guid.NewGuid().ToSqlParam("ID"),
                userId.ToSqlParam("UserID"),
                newRefreshToken.ToSqlParam("Token"),
                DateTime.UtcNow.AddDays(7).ToSqlParam("ExpiresAt"),
                true.ToSqlParam("IsActive"),
                DateTime.UtcNow.ToSqlParam("CreatedAt"));

            var response = new AuthResponse
            {
                User = new UserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    UserType = user.UserType,
                    PreferredLanguage = user.PreferredLanguage,
                    ProfilePicture = user.ProfilePicture,
                    IsEmailVerified = user.IsEmailVerified
                },
                Token = newToken,
                RefreshToken = newRefreshToken
            };

            return ApiResponse<AuthResponse>.SuccessResponse(response, "Token refreshed successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<AuthResponse>.ErrorResponse("REFRESH_TOKEN_FAILED", "Failed to refresh token", ex.Message);
        }
    }

    public async Task<ApiResponse<string>> LogoutAsync(string userId)
    {
        try
        {
            if (!Guid.TryParse(userId, out var userGuid))
            {
                return ApiResponse<string>.ErrorResponse("INVALID_USER_ID", "Invalid user ID");
            }

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // Invalidate all user sessions
            await connection.ExecuteNonQueryText(
                "UPDATE UserSessions SET IsActive = 0 WHERE UserID = @UserID",
                userGuid.ToSqlParam("UserID"));

            return ApiResponse<string>.SuccessResponse("Logged out successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse<string>.ErrorResponse("LOGOUT_FAILED", "Failed to logout", ex.Message);
        }
    }
}
