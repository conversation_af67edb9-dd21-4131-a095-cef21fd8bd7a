using MuslimDirectory.API.Models.DTOs.Auth;
using MuslimDirectory.API.Models.DTOs.Common;

namespace MuslimDirectory.API.Services.Interfaces;

public interface IAuthService
{
    Task<ApiResponse<AuthResponse>> SignupAsync(SignupRequest request);
    Task<ApiResponse<AuthResponse>> SigninAsync(SigninRequest request);
    Task<ApiResponse<AuthResponse>> SocialLoginAsync(SocialLoginRequest request);
    Task<ApiResponse<string>> ForgotPasswordAsync(ForgotPasswordRequest request);
    Task<ApiResponse<string>> ResetPasswordAsync(ResetPasswordRequest request);
    Task<ApiResponse<string>> VerifyEmailAsync(VerifyEmailRequest request);
    Task<ApiResponse<string>> ResendVerificationAsync(ResendVerificationRequest request);
    Task<ApiResponse<AuthResponse>> RefreshTokenAsync(RefreshTokenRequest request);
    Task<ApiResponse<string>> LogoutAsync(string userId);
}
