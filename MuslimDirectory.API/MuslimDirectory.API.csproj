<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4"/>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0"/>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3"/>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\XGENO.DBHelpers\XGENO.DBHelpers.csproj" />
    </ItemGroup>

</Project>
