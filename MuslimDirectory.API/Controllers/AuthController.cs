using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MuslimDirectory.API.Models.DTOs.Auth;
using MuslimDirectory.API.Models.DTOs.Common;
using MuslimDirectory.API.Services.Interfaces;
using System.Security.Claims;

namespace MuslimDirectory.API.Controllers;

[ApiController]
[Route("auth")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;

    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    /// <summary>
    /// Register a new user account
    /// </summary>
    /// <param name="request">User registration details</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("signup")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Signup([FromBody] SignupRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.SignupAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Authenticate user and return access token
    /// </summary>
    /// <param name="request">User login credentials</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("signin")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> Signin([FromBody] SigninRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.SigninAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Authenticate using social providers (Google, Apple)
    /// </summary>
    /// <param name="request">Social login details</param>
    /// <returns>Authentication response with user details and tokens</returns>
    [HttpPost("social-login")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> SocialLogin([FromBody] SocialLoginRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.SocialLoginAsync(request);

        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Request password reset
    /// </summary>
    /// <param name="request">Email for password reset</param>
    /// <returns>Success message</returns>
    [HttpPost("forgot-password")]
    public async Task<ActionResult<ApiResponse<string>>> ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<string>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.ForgotPasswordAsync(request);
        return Ok(result);
    }

    /// <summary>
    /// Reset password using token
    /// </summary>
    /// <param name="request">Reset token and new password</param>
    /// <returns>Success message</returns>
    [HttpPost("reset-password")]
    public async Task<ActionResult<ApiResponse<string>>> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<string>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.ResetPasswordAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Verify email address
    /// </summary>
    /// <param name="request">Email verification token</param>
    /// <returns>Success message</returns>
    [HttpPost("verify-email")]
    public async Task<ActionResult<ApiResponse<string>>> VerifyEmail([FromBody] VerifyEmailRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<string>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.VerifyEmailAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Resend email verification
    /// </summary>
    /// <param name="request">Email for verification resend</param>
    /// <returns>Success message</returns>
    [HttpPost("resend-verification")]
    public async Task<ActionResult<ApiResponse<string>>> ResendVerification([FromBody] ResendVerificationRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<string>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.ResendVerificationAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Refresh access token
    /// </summary>
    /// <param name="request">Current access token</param>
    /// <returns>New authentication response with refreshed tokens</returns>
    [HttpPost("refresh-token")]
    public async Task<ActionResult<ApiResponse<AuthResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState
                .Where(x => x.Value?.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return BadRequest(ApiResponse<AuthResponse>.ErrorResponse("VALIDATION_ERROR", "Validation failed", errors));
        }

        var result = await _authService.RefreshTokenAsync(request);
        
        if (!result.Success)
        {
            return BadRequest(result);
        }

        return Ok(result);
    }

    /// <summary>
    /// Logout user and invalidate tokens
    /// </summary>
    /// <returns>Success message</returns>
    [HttpPost("logout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<string>>> Logout()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        
        if (string.IsNullOrEmpty(userId))
        {
            return BadRequest(ApiResponse<string>.ErrorResponse("INVALID_USER", "Invalid user"));
        }

        var result = await _authService.LogoutAsync(userId);
        return Ok(result);
    }
}
